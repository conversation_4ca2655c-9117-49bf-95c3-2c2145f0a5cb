@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "BadTyp";
  src: url("/fonts/BadTyp.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 47.4% 11.2%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 93.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 1rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    @apply h-full antialiased;
    margin: 0;
    padding: 0;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes zoom-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-10vh) rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

@keyframes bounce-slow {
  0%,
  100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-15px);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes floatStick {
  0%,
  100% {
    transform: translate(0, 0) rotate(var(--rotation));
    opacity: var(--base-opacity);
    filter: brightness(1) blur(0px);
  }
  33% {
    transform: translate(var(--float-x), var(--float-y))
      rotate(calc(var(--rotation) - 2deg));
    opacity: calc(var(--base-opacity) * 1.2);
    filter: brightness(1.3) blur(0.5px);
  }
  66% {
    transform: translate(
        calc(var(--float-x) * -0.5),
        calc(var(--float-y) * -0.5)
      )
      rotate(calc(var(--rotation) + 2deg));
    opacity: calc(var(--base-opacity) * 0.8);
    filter: brightness(0.9) blur(1px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes raindrop-fall {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(150vh);
    opacity: 0.3;
  }
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0) scale(1);
    box-shadow: 0 6px 0 rgba(0, 0, 0, 0.2);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-6px) scale(1.02);
    box-shadow: 0 8px 0 rgba(0, 0, 0, 0.3);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(6px) scale(1.02);
    box-shadow: 0 8px 0 rgba(0, 0, 0, 0.3);
  }
}

@keyframes flash {
  0%,
  50%,
  100% {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0.8;
  }
}

@keyframes sand-fall {
  0% {
    transform: translateY(-30vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  60%,
  80% {
    opacity: 0.9;
  }
  100% {
    transform: translateY(150vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes sand-fall-top {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(40px) scale(0.8);
    opacity: 0;
  }
}

@keyframes sand-funnel {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(40px) scale(0.6);
    opacity: 0.4;
  }
}

@keyframes sand-decrease {
  0%,
  5% {
    d: path("M25 15H75V40C75 55 60 60 50 65C40 60 25 55 25 40V15Z");
    opacity: 0.7;
  }
  95%,
  100% {
    d: path("M25 15H75V30C75 35 60 40 50 45C40 40 25 35 25 30V15Z");
    opacity: 0.5;
  }
}

@keyframes sand-increase {
  0%,
  5% {
    d: path("M25 145L25 125C25 120 35 115 50 110C65 115 75 120 75 125V145H25Z");
    opacity: 0.7;
  }
  95%,
  100% {
    d: path("M25 145L25 115C25 105 35 100 50 95C65 100 75 105 75 115V145H25Z");
    opacity: 0.9;
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes sparkle {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(0) rotate(360deg);
    opacity: 0;
  }
}

@keyframes bounce-very-slow {
  0%,
  100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-8px);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes row-highlight {
  0%,
  100% {
    background-color: rgba(44, 62, 80, 0.5);
  }
  50% {
    background-color: rgba(44, 62, 80, 0.7);
  }
}

@keyframes score-pulse {
  0%,
  100% {
    transform: scale(1);
    text-shadow: 0 0 5px rgba(255, 209, 102, 0.5);
  }
  50% {
    transform: scale(1.05);
    text-shadow: 0 0 15px rgba(255, 209, 102, 0.8);
  }
}

@keyframes progress-pulse {
  0%,
  100% {
    opacity: 0.9;
  }
  50% {
    opacity: 1;
    filter: brightness(1.2);
  }
}

@layer components {
  .glass-card {
    @apply bg-white/80 backdrop-blur-md border border-white/20 shadow-sm;
  }

  .glass-panel {
    @apply bg-white/70 backdrop-blur-lg border border-white/40 shadow-md;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-zoom {
    animation: zoom-pulse 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 2s ease-in-out infinite;
  }

  .animate-confetti {
    animation: confetti-fall 3s linear infinite;
  }

  .animate-bounce-slow {
    animation: bounce-slow 3s ease-in-out infinite;
  }

  /* Leaderboard specific animations */
  .leaderboard-row-highlight {
    animation: row-highlight 2s ease-in-out infinite;
  }

  .leaderboard-score-pulse {
    animation: score-pulse 3s ease-in-out infinite;
  }

  .leaderboard-progress-pulse {
    animation: progress-pulse 2s ease-in-out infinite;
  }

  .confetti-piece {
    position: absolute;
    top: -20px;
    width: 10px;
    height: 10px;
    background-color: #fff;
    animation: confetti-fall 5s linear infinite;
    opacity: 1;
    z-index: 10;
    transform-origin: center;
  }

  .stick {
    animation: floatStick 10s ease-in-out infinite;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity, filter;
    --base-opacity: var(--initial-opacity, 0.1);
    backface-visibility: hidden;
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .stick img {
    filter: brightness(1.5) contrast(0.8);
    transform: translateZ(0);
  }

  .font-badtyp {
    font-family: "BadTyp", sans-serif;
  }

  .raindrop {
    position: absolute;
    top: -20px;
    background-color: #94a3b8;
    border-radius: 0 0 2px 2px;
    animation: raindrop-fall 2s linear infinite;
    opacity: 0.7;
    z-index: 5;
    transform-origin: center top;
    box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
  }

  .animate-heartbeat {
    animation: heartbeat 0.6s ease-in-out infinite;
  }

  .animate-shake {
    animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
  }

  .animate-flash {
    animation: flash 0.8s ease-in-out infinite;
  }

  .sand-particle {
    position: absolute;
    border-radius: 50%;
    animation: sand-fall 8s ease-in infinite;
    z-index: 5;
    box-shadow: 0 0 3px rgba(255, 209, 102, 0.3);
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-sand-fall-top {
    animation: sand-fall-top 1.5s ease-in infinite;
  }

  .animate-sand-funnel {
    animation: sand-funnel 1s ease-in infinite;
  }

  .animate-sand-decrease {
    animation: sand-decrease 10s ease-in-out infinite;
  }

  .animate-sand-increase {
    animation: sand-increase 10s ease-in-out infinite;
  }

  .animate-sparkle {
    animation: sparkle 2s ease-in-out infinite;
  }

  .animate-bounce-very-slow {
    animation: bounce-very-slow 4s ease-in-out infinite;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .gpu {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}
