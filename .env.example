# Example environment variables
# Copy this file to .env.local and update the values for your environment

# API Base URL - Update this to point to your backend server
# For local development: http://172.16.10.201:8000/api
# For production with HTTPS: https://your-backend-domain.com/api
# For production with ngrok: https://abc123.ngrok.io/api
NEXT_PUBLIC_API_BASE_URL=http://172.16.10.201:8000/api

# WebSocket URL - Update this to point to your WebSocket server
# For local development: ws://172.16.10.201:8000/ws
# For production with HTTPS: wss://your-websocket-domain.com/ws
# For production with ngrok: wss://abc123.ngrok.io/ws
NEXT_PUBLIC_WEBSOCKET_URL=ws://172.16.10.201:8000/ws
